
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Star, Plus, Edit, Trash2, Shield, Eye, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Review } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';
import { Timestamp } from 'firebase/firestore';

const AdminReviews = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [tours, setTours] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    tourId: '',
    tourName: '',
    userName: '',
    userAvatar: '',
    rating: 5,
    title: '',
    content: '',
    verified: false
  });

  useEffect(() => {
    loadReviews();
    loadTours();
  }, []);

  const loadReviews = async () => {
    try {
      const reviewsData = await FirebaseService.getAllReviews();
      setReviews(reviewsData as Review[]);
    } catch (error) {
      console.error('Error loading reviews:', error);
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTours = async () => {
    try {
      const toursData = await FirebaseService.getTours();
      setTours(toursData);
    } catch (error) {
      console.error('Error loading tours:', error);
    }
  };

  const handleSubmit = async () => {
    if (!formData.tourId || !formData.userName || !formData.title || !formData.content) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      
      const selectedTour = tours.find(tour => tour.id === formData.tourId);
      const reviewData = {
        ...formData,
        tourName: selectedTour?.title || formData.tourName,
        userId: 'admin-generated',
        images: [],
        helpful: 0,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      if (editingReview) {
        await FirebaseService.updateReview(editingReview.id, {
          ...reviewData,
          updatedAt: Timestamp.now()
        });
        toast({
          title: "Success",
          description: "Review updated successfully"
        });
      } else {
        await FirebaseService.createReview(reviewData);
        toast({
          title: "Success",
          description: "Review created successfully"
        });
      }

      setShowDialog(false);
      setEditingReview(null);
      resetForm();
      loadReviews();
    } catch (error) {
      console.error('Error saving review:', error);
      toast({
        title: "Error",
        description: "Failed to save review",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (review: Review) => {
    setEditingReview(review);
    setFormData({
      tourId: review.tourId,
      tourName: review.tourName,
      userName: review.userName,
      userAvatar: review.userAvatar || '',
      rating: review.rating,
      title: review.title,
      content: review.content,
      verified: review.verified
    });
    setShowDialog(true);
  };

  const handleDelete = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review?')) return;

    try {
      await FirebaseService.deleteReview(reviewId);
      toast({
        title: "Success",
        description: "Review deleted successfully"
      });
      loadReviews();
    } catch (error) {
      console.error('Error deleting review:', error);
      toast({
        title: "Error",
        description: "Failed to delete review",
        variant: "destructive"
      });
    }
  };

  const resetForm = () => {
    setFormData({
      tourId: '',
      tourName: '',
      userName: '',
      userAvatar: '',
      rating: 5,
      title: '',
      content: '',
      verified: false
    });
  };

  const RatingStars = ({ rating }: { rating: number }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Reviews Management</h1>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => { resetForm(); setEditingReview(null); }} className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Review
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{editingReview ? 'Edit Review' : 'Add New Review'}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tour">Tour</Label>
                  <Select value={formData.tourId} onValueChange={(value) => setFormData(prev => ({ ...prev, tourId: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a tour" />
                    </SelectTrigger>
                    <SelectContent>
                      {tours.map((tour) => (
                        <SelectItem key={tour.id} value={tour.id}>
                          {tour.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="userName">User Name</Label>
                  <Input
                    id="userName"
                    value={formData.userName}
                    onChange={(e) => setFormData(prev => ({ ...prev, userName: e.target.value }))}
                    placeholder="Reviewer name"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="rating">Rating</Label>
                  <Select value={formData.rating.toString()} onValueChange={(value) => setFormData(prev => ({ ...prev, rating: parseInt(value) }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <SelectItem key={rating} value={rating.toString()}>
                          {rating} Star{rating !== 1 ? 's' : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <input
                    type="checkbox"
                    id="verified"
                    checked={formData.verified}
                    onChange={(e) => setFormData(prev => ({ ...prev, verified: e.target.checked }))}
                  />
                  <Label htmlFor="verified">Verified Review</Label>
                </div>
              </div>

              <div>
                <Label htmlFor="userAvatar">User Avatar URL (optional)</Label>
                <Input
                  id="userAvatar"
                  value={formData.userAvatar}
                  onChange={(e) => setFormData(prev => ({ ...prev, userAvatar: e.target.value }))}
                  placeholder="https://example.com/avatar.jpg"
                />
              </div>

              <div>
                <Label htmlFor="title">Review Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Review title"
                />
              </div>

              <div>
                <Label htmlFor="content">Review Content</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Review content"
                  rows={4}
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleSubmit} disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    editingReview ? 'Update Review' : 'Create Review'
                  )}
                </Button>
                <Button variant="outline" onClick={() => setShowDialog(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row justify-between items-start gap-4 mb-4">
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                    <h3 className="font-semibold">{review.userName}</h3>
                    {review.verified && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        <Shield className="w-3 h-3 mr-1" />
                        Verified
                      </Badge>
                    )}
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                    <RatingStars rating={review.rating} />
                    <span className="text-sm text-gray-600">
                      {review.createdAt?.toDate?.()?.toLocaleDateString() || 'Date not available'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">Tour: {review.tourName}</p>
                  <h4 className="font-medium mb-2">{review.title}</h4>
                  <p className="text-gray-700">{review.content}</p>
                </div>
                <div className="flex gap-2 w-full lg:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(review)}
                    className="flex-1 lg:flex-none"
                  >
                    <Edit className="h-4 w-4 mr-2 lg:mr-0" />
                    <span className="lg:hidden">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(review.id)}
                    className="flex-1 lg:flex-none"
                  >
                    <Trash2 className="h-4 w-4 mr-2 lg:mr-0" />
                    <span className="lg:hidden">Delete</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {reviews.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Eye className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">No Reviews Found</h3>
            <p className="text-gray-600">Start by adding your first review.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminReviews;
