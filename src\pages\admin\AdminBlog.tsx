
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Eye, FileText, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { BlogPost } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';
import { Timestamp } from 'firebase/firestore';

const AdminBlog = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    authorName: 'Admin',
    category: '',
    tags: '',
    published: false,
    wildlifeSpotted: '',
    photographyTips: '',
    conservationMessage: '',
    culturalInsights: '',
    seasonalRelevance: ''
  });

  useEffect(() => {
    loadBlogPosts();
  }, []);

  const loadBlogPosts = async () => {
    try {
      const postsData = await FirebaseService.getBlogPosts();
      setBlogPosts(postsData as BlogPost[]);
    } catch (error) {
      console.error('Error loading blog posts:', error);
      toast({
        title: "Error",
        description: "Failed to load blog posts",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.content || !formData.excerpt) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      
      const slug = formData.slug || generateSlug(formData.title);
      const postData = {
        ...formData,
        slug,
        authorId: 'admin',
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        wildlifeSpotted: formData.wildlifeSpotted.split(',').map(item => item.trim()).filter(item => item),
        photographyTips: formData.photographyTips.split(',').map(tip => tip.trim()).filter(tip => tip),
        culturalInsights: formData.culturalInsights.split(',').map(insight => insight.trim()).filter(insight => insight),
        seasonalRelevance: formData.seasonalRelevance.split(',').map(season => season.trim()).filter(season => season),
        viewCount: 0,
        publishedAt: formData.published ? Timestamp.now() : null,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      if (editingPost) {
        await FirebaseService.updateBlogPost(editingPost.id, {
          ...postData,
          updatedAt: Timestamp.now()
        });
        toast({
          title: "Success",
          description: "Blog post updated successfully"
        });
      } else {
        await FirebaseService.createBlogPost(postData);
        toast({
          title: "Success",
          description: "Blog post created successfully"
        });
      }

      setShowDialog(false);
      setEditingPost(null);
      resetForm();
      loadBlogPosts();
    } catch (error) {
      console.error('Error saving blog post:', error);
      toast({
        title: "Error",
        description: "Failed to save blog post",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (post: BlogPost) => {
    setEditingPost(post);
    setFormData({
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      featuredImage: post.featuredImage,
      authorName: post.authorName,
      category: post.category,
      tags: post.tags.join(', '),
      published: post.published,
      wildlifeSpotted: post.wildlifeSpotted?.join(', ') || '',
      photographyTips: post.photographyTips?.join(', ') || '',
      conservationMessage: post.conservationMessage || '',
      culturalInsights: post.culturalInsights?.join(', ') || '',
      seasonalRelevance: post.seasonalRelevance?.join(', ') || ''
    });
    setShowDialog(true);
  };

  const handleDelete = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this blog post?')) return;

    try {
      await FirebaseService.deleteBlogPost(postId);
      toast({
        title: "Success",
        description: "Blog post deleted successfully"
      });
      loadBlogPosts();
    } catch (error) {
      console.error('Error deleting blog post:', error);
      toast({
        title: "Error",
        description: "Failed to delete blog post",
        variant: "destructive"
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      featuredImage: '',
      authorName: 'Admin',
      category: '',
      tags: '',
      published: false,
      wildlifeSpotted: '',
      photographyTips: '',
      conservationMessage: '',
      culturalInsights: '',
      seasonalRelevance: ''
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Blog Management</h1>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => { resetForm(); setEditingPost(null); }} className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Blog Post
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingPost ? 'Edit Blog Post' : 'Add New Blog Post'}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      title: e.target.value,
                      slug: generateSlug(e.target.value)
                    }))}
                    placeholder="Blog post title"
                  />
                </div>
                <div>
                  <Label htmlFor="slug">Slug</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="blog-post-url"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="excerpt">Excerpt *</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  placeholder="Brief description of the blog post"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="content">Content *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Full blog post content"
                  rows={8}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="featuredImage">Featured Image URL</Label>
                  <Input
                    id="featuredImage"
                    value={formData.featuredImage}
                    onChange={(e) => setFormData(prev => ({ ...prev, featuredImage: e.target.value }))}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    placeholder="Wildlife, Culture, Photography, etc."
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="tags">Tags (comma-separated)</Label>
                <Input
                  id="tags"
                  value={formData.tags}
                  onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder="safari, wildlife, tanzania, photography"
                />
              </div>

              <div>
                <Label htmlFor="wildlifeSpotted">Wildlife Spotted (comma-separated)</Label>
                <Input
                  id="wildlifeSpotted"
                  value={formData.wildlifeSpotted}
                  onChange={(e) => setFormData(prev => ({ ...prev, wildlifeSpotted: e.target.value }))}
                  placeholder="Lion, Elephant, Leopard"
                />
              </div>

              <div>
                <Label htmlFor="photographyTips">Photography Tips (comma-separated)</Label>
                <Input
                  id="photographyTips"
                  value={formData.photographyTips}
                  onChange={(e) => setFormData(prev => ({ ...prev, photographyTips: e.target.value }))}
                  placeholder="Use telephoto lens, Golden hour lighting"
                />
              </div>

              <div>
                <Label htmlFor="conservationMessage">Conservation Message</Label>
                <Textarea
                  id="conservationMessage"
                  value={formData.conservationMessage}
                  onChange={(e) => setFormData(prev => ({ ...prev, conservationMessage: e.target.value }))}
                  placeholder="Conservation insights and messages"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="culturalInsights">Cultural Insights (comma-separated)</Label>
                <Input
                  id="culturalInsights"
                  value={formData.culturalInsights}
                  onChange={(e) => setFormData(prev => ({ ...prev, culturalInsights: e.target.value }))}
                  placeholder="Maasai culture, Traditional practices"
                />
              </div>

              <div>
                <Label htmlFor="seasonalRelevance">Seasonal Relevance (comma-separated)</Label>
                <Input
                  id="seasonalRelevance"
                  value={formData.seasonalRelevance}
                  onChange={(e) => setFormData(prev => ({ ...prev, seasonalRelevance: e.target.value }))}
                  placeholder="Dry season, Green season, Migration"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="published"
                  checked={formData.published}
                  onChange={(e) => setFormData(prev => ({ ...prev, published: e.target.checked }))}
                />
                <Label htmlFor="published">Publish immediately</Label>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleSubmit} disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    editingPost ? 'Update Post' : 'Create Post'
                  )}
                </Button>
                <Button variant="outline" onClick={() => setShowDialog(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {blogPosts.map((post) => (
          <Card key={post.id}>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row justify-between items-start gap-4">
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                    <h3 className="font-semibold text-lg">{post.title}</h3>
                    <Badge variant={post.published ? "default" : "secondary"}>
                      {post.published ? "Published" : "Draft"}
                    </Badge>
                  </div>
                  <p className="text-gray-600 mb-2">{post.excerpt}</p>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-gray-500 mb-2">
                    <span>By {post.authorName}</span>
                    <span>Category: {post.category}</span>
                    <span className="hidden sm:inline">Views: {post.viewCount}</span>
                    <span>
                      {post.publishedAt?.toDate?.()?.toLocaleDateString() ||
                       post.createdAt?.toDate?.()?.toLocaleDateString() ||
                       'Date not available'}
                    </span>
                  </div>
                  {post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {post.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                <div className="flex gap-2 w-full lg:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(post)}
                    className="flex-1 lg:flex-none"
                  >
                    <Edit className="h-4 w-4 mr-2 lg:mr-0" />
                    <span className="lg:hidden">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(post.id)}
                    className="flex-1 lg:flex-none"
                  >
                    <Trash2 className="h-4 w-4 mr-2 lg:mr-0" />
                    <span className="lg:hidden">Delete</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {blogPosts.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">No Blog Posts Found</h3>
            <p className="text-gray-600">Start by creating your first blog post.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdminBlog;
