
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Users, Star, MessageSquare, Plane, FileText, Images } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';

const AdminOverview = () => {
  const [stats, setStats] = useState({
    totalTours: 0,
    totalBookings: 0,
    totalUsers: 0,
    totalReviews: 0,
    totalMessages: 0,
    totalBlogPosts: 0,
    totalGalleryImages: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [tours, bookings, users, reviews, messages, blogPosts, galleryImages] = await Promise.all([
        FirebaseService.getTours(),
        FirebaseService.getAllBookings(),
        FirebaseService.getAllUsers(),
        FirebaseService.getAllReviews(),
        FirebaseService.getAllMessages(),
        FirebaseService.getBlogPosts(),
        FirebaseService.getGalleryImages()
      ]);

      setStats({
        totalTours: tours.length,
        totalBookings: bookings.length,
        totalUsers: users.length,
        totalReviews: reviews.length,
        totalMessages: messages.length,
        totalBlogPosts: blogPosts.length,
        totalGalleryImages: galleryImages.length
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Total Tours',
      value: stats.totalTours,
      icon: Plane,
      color: 'text-blue-600'
    },
    {
      title: 'Total Bookings',
      value: stats.totalBookings,
      icon: Calendar,
      color: 'text-green-600'
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-purple-600'
    },
    {
      title: 'Total Reviews',
      value: stats.totalReviews,
      icon: Star,
      color: 'text-yellow-600'
    },
    {
      title: 'Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'text-red-600'
    },
    {
      title: 'Blog Posts',
      value: stats.totalBlogPosts,
      icon: FileText,
      color: 'text-indigo-600'
    },
    {
      title: 'Gallery Images',
      value: stats.totalGalleryImages,
      icon: Images,
      color: 'text-pink-600'
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 7 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-16 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold">Admin Dashboard</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs md:text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-xl md:text-2xl font-bold">{stat.value.toLocaleString()}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">Add New Tour</div>
                <div className="text-xs md:text-sm text-gray-600">Create a new safari tour</div>
              </button>
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">Add Blog Post</div>
                <div className="text-xs md:text-sm text-gray-600">Write a new blog article</div>
              </button>
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">Add Gallery Image</div>
                <div className="text-xs md:text-sm text-gray-600">Upload new photos</div>
              </button>
              <button className="p-3 md:p-4 border rounded-lg hover:bg-gray-50 text-left">
                <div className="font-medium text-sm md:text-base">View Messages</div>
                <div className="text-xs md:text-sm text-gray-600">Check customer inquiries</div>
              </button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                No recent activity to display. Start managing your content to see updates here.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminOverview;
