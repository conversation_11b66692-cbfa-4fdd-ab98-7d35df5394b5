import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY || 'your_gemini_api_key_here';
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

const SYSTEM_PROMPT = `You are an expert safari guide and travel assistant for Warrior Safari, a premium safari tour company in Tanzania. You have extensive knowledge about:

**COMPANY INFORMATION:**
- Company Name: Warrior Safari (formerly Safari Sole)
- Location: Arusha, Tanzania
- Contact: <EMAIL>, +*********** 456
- Specializes in wildlife safaris, cultural tours, adventure experiences, and luxury packages
- Operating since 2009 with conservation partnerships and community programs

**DESTINATIONS & PARKS:**
- Serengeti National Park: Famous for Great Migration, Big Five, endless plains
- Ngorongoro Crater: UNESCO World Heritage site, natural amphitheater with dense wildlife
- Tarangire National Park: Known for large elephant herds, baobab trees, diverse birdlife
- Lake Manyara: Tree-climbing lions, flamingos, diverse ecosystems
- Mount Kilimanjaro: Africa's highest peak, climbing expeditions
- Maasai Villages: Cultural experiences, traditional lifestyle

**TOUR CATEGORIES:**
- Wildlife Safari: Game drives, Big Five viewing, migration experiences
- Cultural Tours: Maasai village visits, local traditions, community interactions
- Adventure Tours: Kilimanjaro climbing, walking safaris, camping experiences
- Photography Tours: Specialized for wildlife photography with expert guides
- Luxury Safaris: High-end accommodations, private guides, exclusive experiences

**ACCOMMODATION LEVELS:**
- Budget: $200-400/day - Basic lodges, camping, shared facilities
- Mid-range: $400-800/day - Comfortable lodges, private rooms, good amenities
- Luxury: $800-1500+/day - Premium lodges, exclusive camps, full service

**BEST TIMES TO VISIT:**
- Dry Season (June-October): Best wildlife viewing, Great Migration in Serengeti
- Wet Season (November-May): Green landscapes, calving season, fewer crowds
- Migration Calendar: Varies by location and month

**WHAT'S TYPICALLY INCLUDED:**
- Accommodation (based on selected level)
- All meals during safari
- Professional safari guide
- Park entrance fees
- Game drives in 4WD vehicles
- Airport transfers

**BOOKING PROCESS:**
- Browse tours on website
- Use tour builder for custom experiences
- Contact for personalized quotes
- Secure booking with deposit
- Final payment before departure

**SAFETY & REQUIREMENTS:**
- Valid passport required
- Yellow fever vaccination recommended
- Travel insurance advised
- Professional guides ensure safety
- Well-maintained vehicles

**CONSERVATION COMMITMENT:**
- Partnership with Tanzania Wildlife Authority
- Community development programs
- Responsible tourism practices
- Wildlife conservation support

**INSTRUCTIONS:**
1. Always be helpful, knowledgeable, and enthusiastic about safari experiences
2. Provide specific, accurate information about destinations, wildlife, and tours
3. Help users navigate the website and find relevant information
4. Suggest appropriate tours based on user preferences and budget
5. Encourage bookings while being informative, not pushy
6. If you don't know specific current pricing or availability, direct users to contact the company
7. Always maintain the excitement and wonder of safari experiences
8. Be culturally sensitive when discussing local communities and traditions

**RESPONSE STYLE:**
- Friendly and professional
- Use safari and wildlife terminology appropriately
- Include practical tips and insights
- Suggest next steps or actions when relevant
- Keep responses concise but informative

Remember: You represent Warrior Safari and should embody the expertise and passion of a professional safari guide while helping users plan their dream African adventure.`;

export class GeminiService {
  private model;

  constructor() {
    this.model = genAI.getGenerativeModel({ 
      model: 'gemini-1.5-flash',
      systemInstruction: SYSTEM_PROMPT
    });
  }

  async generateResponse(userMessage: string, conversationHistory: Array<{role: string, content: string}> = []): Promise<{
    text: string;
    suggestions?: string[];
    actions?: Array<{ label: string; action: string; data?: any }>;
  }> {
    try {
      // Build conversation context
      const context = conversationHistory.map(msg => 
        `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`
      ).join('\n');
      
      const fullPrompt = context ? `${context}\nUser: ${userMessage}` : `User: ${userMessage}`;

      const result = await this.model.generateContent(fullPrompt);
      const response = await result.response;
      const text = response.text();

      // Generate contextual suggestions and actions based on the response
      const suggestions = this.generateSuggestions(userMessage, text);
      const actions = this.generateActions(userMessage, text);

      return {
        text,
        suggestions,
        actions
      };
    } catch (error) {
      console.error('Error generating Gemini response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private generateSuggestions(userMessage: string, aiResponse: string): string[] {
    const lowerMessage = userMessage.toLowerCase();
    const lowerResponse = aiResponse.toLowerCase();

    // Context-aware suggestions based on user query and AI response
    if (lowerMessage.includes('tour') || lowerResponse.includes('tour')) {
      return ['Show me tour prices', 'Best time to visit', 'What\'s included in tours?', 'Custom tour options'];
    }
    
    if (lowerMessage.includes('destination') || lowerResponse.includes('serengeti') || lowerResponse.includes('ngorongoro')) {
      return ['Wildlife in each park', 'Best photography spots', 'Accommodation options', 'Park entrance fees'];
    }
    
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerResponse.includes('budget')) {
      return ['Payment options', 'Group discounts', 'What\'s included?', 'Booking process'];
    }
    
    if (lowerMessage.includes('book') || lowerMessage.includes('reservation')) {
      return ['Available dates', 'Group size options', 'Special requirements', 'Cancellation policy'];
    }

    // Default suggestions
    return ['Browse tours', 'Contact information', 'Best destinations', 'Safari tips'];
  }

  private generateActions(userMessage: string, aiResponse: string): Array<{ label: string; action: string; data?: any }> {
    const lowerMessage = userMessage.toLowerCase();
    const lowerResponse = aiResponse.toLowerCase();
    const actions: Array<{ label: string; action: string; data?: any }> = [];

    // Tour-related actions
    if (lowerMessage.includes('tour') || lowerResponse.includes('tour')) {
      actions.push({ label: 'Browse All Tours', action: 'navigate', data: '/tours' });
      actions.push({ label: 'Custom Tour Builder', action: 'navigate', data: '/tour-builder' });
    }

    // Destination actions
    if (lowerMessage.includes('destination') || lowerResponse.includes('park')) {
      actions.push({ label: 'View Destinations', action: 'navigate', data: '/destinations' });
    }

    // Contact actions
    if (lowerMessage.includes('contact') || lowerMessage.includes('book') || lowerResponse.includes('contact')) {
      actions.push({ label: 'Contact Us', action: 'navigate', data: '/contact' });
      actions.push({ label: 'Send Email', action: 'email', data: '<EMAIL>' });
    }

    // Specific destination searches
    if (lowerResponse.includes('serengeti')) {
      actions.push({ label: 'Serengeti Tours', action: 'search', data: 'serengeti' });
    }
    if (lowerResponse.includes('ngorongoro')) {
      actions.push({ label: 'Ngorongoro Tours', action: 'search', data: 'ngorongoro' });
    }

    return actions;
  }

  // Method to analyze website content for better context (future enhancement)
  async analyzeWebContent(url: string): Promise<string> {
    // This would be implemented to crawl and analyze website content
    // For now, return empty string as placeholder
    return '';
  }
}

export const geminiService = new GeminiService();
